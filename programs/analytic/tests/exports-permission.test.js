import test from 'ava';

test.beforeEach(async t => {
    // Mock app and user objects for testing
    t.context.app = {
        checkPermission: async (payload) => {
            const { name, user } = payload;
            
            // Mock permission check based on user type
            if (user.hasRestrictedAccess && name === 'analytic.canOnlySeeOwnRecordsInAnalytics') {
                return true;
            }
            return false;
        }
    };
    
    t.context.normalUser = {
        _id: 'user1',
        code: 'USER001',
        name: 'Normal User',
        hasRestrictedAccess: false,
        isRoot: false
    };
    
    t.context.restrictedUser = {
        _id: 'user2', 
        code: 'USER002',
        name: 'Restricted User',
        hasRestrictedAccess: true,
        isRoot: false
    };
    
    t.context.rootUser = {
        _id: 'root',
        code: 'ROOT',
        name: 'Root User',
        isRoot: true
    };
});

test('exports collection hook - normal user sees all exports', async t => {
    const { app, normalUser } = t.context;
    
    // Mock context for normal user
    const context = {
        app,
        params: {
            provider: 'rest',
            user: normalUser,
            query: {}
        }
    };
    
    // Import and test the hook function
    const exportsCollection = await import('../collections/exports.js');
    const hookFunction = exportsCollection.default.hooks.before.find;
    
    const result = await hookFunction(context);
    
    // Normal user should not have filtering applied
    t.is(result.params.query.$and, undefined);
});

test('exports collection hook - restricted user sees only own exports', async t => {
    const { app, restrictedUser } = t.context;
    
    // Mock context for restricted user
    const context = {
        app,
        params: {
            provider: 'rest',
            user: restrictedUser,
            query: {}
        }
    };
    
    // Import and test the hook function
    const exportsCollection = await import('../collections/exports.js');
    const hookFunction = exportsCollection.default.hooks.before.find;
    
    const result = await hookFunction(context);
    
    // Restricted user should have filtering applied
    t.truthy(result.params.query.$and);
    t.is(result.params.query.$and.length, 1);
    t.deepEqual(result.params.query.$and[0], { createdBy: restrictedUser._id });
});

test('exports collection hook - root user bypasses filtering', async t => {
    const { app, rootUser } = t.context;
    
    // Mock context for root user
    const context = {
        app,
        params: {
            provider: 'rest',
            user: rootUser,
            query: {}
        }
    };
    
    // Import and test the hook function
    const exportsCollection = await import('../collections/exports.js');
    const hookFunction = exportsCollection.default.hooks.before.find;
    
    const result = await hookFunction(context);
    
    // Root user should not have filtering applied
    t.is(result.params.query.$and, undefined);
});

test('exports collection hook - server requests bypass filtering', async t => {
    const { app, restrictedUser } = t.context;
    
    // Mock context for server request (no provider)
    const context = {
        app,
        params: {
            user: restrictedUser,
            query: {}
        }
    };
    
    // Import and test the hook function
    const exportsCollection = await import('../collections/exports.js');
    const hookFunction = exportsCollection.default.hooks.before.find;
    
    const result = await hookFunction(context);
    
    // Server requests should not have filtering applied
    t.is(result.params.query.$and, undefined);
});

test('exports collection hook - preserves existing $and conditions', async t => {
    const { app, restrictedUser } = t.context;
    
    // Mock context with existing $and conditions
    const context = {
        app,
        params: {
            provider: 'rest',
            user: restrictedUser,
            query: {
                $and: [{ status: 'completed' }]
            }
        }
    };
    
    // Import and test the hook function
    const exportsCollection = await import('../collections/exports.js');
    const hookFunction = exportsCollection.default.hooks.before.find;
    
    const result = await hookFunction(context);
    
    // Should preserve existing conditions and add user filter
    t.truthy(result.params.query.$and);
    t.is(result.params.query.$and.length, 2);
    t.deepEqual(result.params.query.$and[0], { status: 'completed' });
    t.deepEqual(result.params.query.$and[1], { createdBy: restrictedUser._id });
});
