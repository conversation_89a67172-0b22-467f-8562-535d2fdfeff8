export default {
    name: 'exports',
    title: 'Exports',
    schema: {
        status: {
            type: 'string',
            label: 'Status',
            default: 'in-progress',
            index: true
        },
        title: {
            type: 'string',
            label: 'Title',
            index: true
        },
        message: {
            type: 'string',
            label: 'Message',
            required: false,
            index: true
        },
        fileId: {
            type: 'string',
            label: 'File',
            required: false
        },
        startDate: {
            type: 'datetime',
            label: 'Start date',
            default: 'date:now',
            index: true
        },
        endDate: {
            type: 'datetime',
            label: 'End date',
            required: false,
            index: true
        },
        userCode: {
            type: 'string',
            label: 'User code',
            required: false,
            index: true
        },
        userName: {
            type: 'string',
            label: 'User name',
            required: false,
            index: true
        }
    },
    hooks: {
        before: {
            find: async function(context) {
                const app = context.app;
                const user = context.params.user;

                // Skip filtering for server requests or if user is not authenticated
                if (!context.params.provider || !user || user.isRoot) {
                    return context;
                }

                // Check if user has basic read permission for exports
                let hasReadPermission = false;
                try {
                    hasReadPermission = await app.checkPermission({
                        type: 'record',
                        collection: 'analytic.exports',
                        method: 'find',
                        user
                    });
                } catch (error) {
                    // No read permission, return empty result
                    if (!context.params.query) {
                        context.params.query = {};
                    }
                    // Add impossible condition to return empty result
                    context.params.query._id = 'no-access';
                    return context;
                }

                // Check if user has permission to only see own records in analytics
                let canOnlySeeOwnRecords = false;
                try {
                    canOnlySeeOwnRecords = await app.checkPermission({
                        type: 'permission',
                        name: 'analytic.canOnlySeeOwnRecordsInAnalytics',
                        user
                    });
                } catch (error) {
                    // Permission not granted, continue without filtering
                }

                // If user has the restriction permission, filter to show only their own exports
                if (canOnlySeeOwnRecords) {
                    if (!context.params.query) {
                        context.params.query = {};
                    }

                    // Filter by createdBy field to show only user's own exports
                    if (!Array.isArray(context.params.query.$and)) {
                        context.params.query.$and = [];
                    }

                    context.params.query.$and.push({
                        createdBy: user._id
                    });
                }

                return context;
            }
        }
    }
};
