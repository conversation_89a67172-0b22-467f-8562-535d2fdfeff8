<template>
    <ui-view
        class="production-reports-raw-material-analysis"
        type="content"
        :left-panel-width="400"
        v-if="isInitialized"
    >
        <template slot="top-panel">
            <ui-scope
                id="production.reports.raw-material-analysis"
                :filters="scopeApplicableFilters"
                @changed="handleScopeChange"
            />
        </template>

        <template slot="left-panel">
            <ui-list
                ref="orderList"
                collection="production.orders"
                :extra-fields="['code', 'product.code', 'product.definition', 'product.image']"
                enable-search
                single-select
                style="background-color: white"
                :item-height="60"
                :html-template="getOrdersCellTemplate"
                @selected-items="handleSelect"
            />
        </template>

        <div class="order-items">
            <ui-table
                ref="mainTable"
                id="production.reports-raw-material-analysis"
                :get-rows="getRows"
                :columns="columns"
                :filters="taskFilters"
                :summary-row="summaryRow"
                :enable-sorting="false"
                :view-has-export="true"
                :enable-selection="true"
                :single-select="true"
            />
        </div>
    </ui-view>
</template>

<script>
import _ from 'lodash';
import fastCopy from 'fast-copy';

export default {
    data: () => ({
        isInitialized: false,
        scopeQuery: {},
        selected: null
    }),

    computed: {
        taskFilters() {
            const filters = fastCopy(this.scopeQuery);

            if (_.isPlainObject(this.selected)) {
                filters.orderId = this.selected._id;
            }

            return filters;
        },
        scopeApplicableFilters() {
            const self = this;

            return [
                {code: 'today', label: 'Today', query: 'order.orderDate|today'},
                {
                    code: 'yesterday',
                    label: 'Yesterday',
                    query: 'order.orderDate|yesterday'
                },
                {
                    code: 'thisWeek',
                    label: 'This week',
                    query: 'order.orderDate|thisWeek'
                },
                {
                    code: 'lastWeek',
                    label: 'Last week',
                    query: 'order.orderDate|lastWeek'
                },
                {
                    code: 'thisMonth',
                    label: 'This month',
                    query: 'order.orderDate|thisMonth'
                },
                {
                    code: 'lastMonth',
                    label: 'Last month',
                    query: 'order.orderDate|lastMonth'
                },
                {
                    code: 'thisQuarter',
                    label: 'This quarter',
                    query: 'order.orderDate|thisQuarter'
                },
                {
                    code: 'lastQuarter',
                    label: 'Last quarter',
                    query: 'order.orderDate|lastQuarter'
                },

                {field: 'order.code', label: 'Order Code'},
                {
                    field: 'order.orderDate',
                    code: 'orderDate',
                    label: 'Order date',
                    type: 'date'
                },
                {
                    field: 'order.plannedStartDate',
                    label: 'Planned start date',
                    type: 'date'
                },
                {
                    field: 'order.plannedEndDate',
                    label: 'Planned end date',
                    type: 'date'
                },
                {
                    field: 'order.branchId',
                    label: 'Branch office',
                    collection: 'kernel.branches',
                    filters: {
                        _id: {$in: this.$user.branchIds},
                        $sort: {name: 1}
                    },
                    condition() {
                        return self.$setting('system.multiBranch');
                    }
                },
                {
                    field: 'order.warehouseId',
                    label: 'Warehouse',
                    collection: 'inventory.warehouses',
                    filters: {$sort: {name: 1}}
                },
                {
                    field: 'order.relatedPartnerId',
                    label: 'Related partner',
                    collection: 'kernel.partners'
                },
                {
                    field: 'order.status',
                    label: 'Order Status',
                    translateLabels: true,
                    items: [
                        {value: 'draft', label: 'Draft'},
                        {value: 'planning', label: 'Planning'},
                        {value: 'producing', label: 'Producing'},
                        {value: 'completed', label: 'Completed'},
                        {value: 'canceled', label: 'Canceled'}
                    ]
                },
                {
                    field: 'status',
                    label: 'Task Status',
                    translateLabels: true,
                    items: [
                        {value: 'draft', label: 'Draft'},
                        {value: 'waiting', label: 'Waiting'},
                        {value: 'in-progress', label: 'In Progress'},
                        {value: 'stopped', label: 'Stopped'},
                        {value: 'awaiting-procurement', label: 'Awaiting Procurement'},
                        {value: 'partially-completed', label: 'Partially Completed'},
                        {value: 'completed', label: 'Completed'},
                        {value: 'canceled', label: 'Canceled'}
                    ]
                }
            ];
        },
        columns() {
            const self = this;

            return [
                {
                    field: 'order.code',
                    label: 'Order Code',
                    width: 120,
                    relationParams(params) {
                        const data = params.data;

                        return {
                            id: data.orderId,
                            view: 'production.production.orders'
                        };
                    }
                },
                {
                    field: 'order.orderDate',
                    label: 'Order Date',
                    format: 'date',
                    sort: 'desc',
                    width: 120
                },
                {
                    field: 'productCode',
                    label: 'Input Product Code',
                    relationParams(params) {
                        const data = params.data;
                        const relation = {};

                        relation.isVisible = _.isString(data.productId);

                        if (relation.isVisible) {
                            relation.view = 'inventory.catalog.products';
                            relation.id = data.productId;
                            relation.template = data.productCode;
                        }

                        return relation;
                    },
                    width: 150
                },
                {
                    field: 'productDefinition',
                    label: 'Input Product Definition',
                    relationParams(params) {
                        const data = params.data;
                        const relation = {};

                        relation.isVisible = _.isString(data.productId);

                        if (relation.isVisible) {
                            relation.view = 'inventory.catalog.products';
                            relation.id = data.productId;
                            relation.template = data.productDefinition;
                        }

                        return relation;
                    },
                    minWidth: 210
                },
                {
                    field: 'requiredDate',
                    label: 'Required Date',
                    format: 'datetime',
                    width: 155
                },
                {
                    field: 'plannedQuantity',
                    label: 'Required Quantity',
                    width: 120,
                    format: 'unit',
                    formatOptions() {
                        return {number: {precision: self.$setting('system.unitPrecision')}};
                    }
                },
                {
                    field: 'actualQuantity',
                    label: 'Actual Quantity',
                    width: 120,
                    format: 'unit',
                    formatOptions() {
                        return {number: {precision: self.$setting('system.unitPrecision')}};
                    }
                },
                {
                    field: 'unitName',
                    label: 'Unit',
                    width: 90
                },
                {
                    field: 'backflush',
                    label: 'Backflush',
                    width: 100,
                    render: params => {
                        return params.data.backflush ? this.$t('Yes') : this.$t('No');
                    }
                },
                {
                    field: 'plannedCost',
                    label: 'Planned cost',
                    width: 120,
                    format: 'currency',
                    hidden: () =>
                        !this.$checkPermission({
                            type: 'permission',
                            name: 'system.canSeeCosts'
                        })
                },
                {
                    field: 'actualCost',
                    label: 'Actual cost',
                    width: 120,
                    format: 'currency',
                    hidden: () =>
                        !this.$checkPermission({
                            type: 'permission',
                            name: 'system.canSeeCosts'
                        })
                },
                {
                    field: 'order.status',
                    label: 'Order Status',
                    tagsCell: true,
                    translateLabels: true,
                    tagLabels: [
                        {value: 'draft', label: 'Draft', color: 'default'},
                        {value: 'planning', label: 'Planning', color: 'teal'},
                        {value: 'producing', label: 'Producing', color: 'success'},
                        {value: 'completed', label: 'Completed', color: 'primary'},
                        {value: 'canceled', label: 'Canceled', color: 'danger'}
                    ],
                    width: 150
                }
            ];
        }
    },

    methods: {
        getOrdersCellTemplate(item) {
            const imageUrl = item.product && item.product.image
                ? this.$app.absoluteUrl(`files/${item.product.image}`)
                : this.$app.absoluteUrl('static/images/no-image.png');
            return `
            <div class="production-order-cell">
                <div class="order-cell-container">
                    <img class="order-cell-image" src="${imageUrl}" />
                </div>
                <div class="order-cell-content">
                    <div class="order-cell-code">${item.code}</div>
                    <div class="order-cell-product">
                        ${item.product ? item.product.definition || item.product.code : 'N/A'}
                    </div>
                </div>
            </div>
            `.trim();
        },
        async handleScopeChange(model) {
            this.scopeQuery = model.query;
        },
        async handleSelect(selected) {
            this.selected = selected = selected[0];
        },
        async getRows(tableQuery, params) {
            try {
                const result = {
                    total: 0,
                    data: []
                };

                const startRow = params.request.startRow;
                const endRow = params.request.endRow;

                const taskFilters = fastCopy(this.taskFilters);
                let orderDateFilter = null;
                let orderIdFilter = null;
                if (taskFilters['order.orderDate']) {
                    orderDateFilter = taskFilters['order.orderDate'];
                    delete taskFilters['order.orderDate'];
                }
                if (taskFilters['orderId']) {
                    orderIdFilter = taskFilters['orderId'];
                    delete taskFilters['orderId'];
                }

                let tableOrderDateFilter = null;
                let tableOrderIdFilter = null;
                if (tableQuery && Object.keys(tableQuery).length > 0) {
                    const cleanTableQuery = fastCopy(tableQuery);
                    delete cleanTableQuery.$skip;
                    delete cleanTableQuery.$limit;
                    delete cleanTableQuery.$paginate;
                    delete cleanTableQuery.$select;
                    if (cleanTableQuery['order.orderDate']) {
                        tableOrderDateFilter = cleanTableQuery['order.orderDate'];
                        delete cleanTableQuery['order.orderDate'];
                    }
                    if (cleanTableQuery['orderId']) {
                        tableOrderIdFilter = cleanTableQuery['orderId'];
                        delete cleanTableQuery['orderId'];
                    }
                    Object.assign(taskFilters, cleanTableQuery);
                }

                let finalOrderDateFilter = tableOrderDateFilter || orderDateFilter;
                let finalOrderIdFilter = tableOrderIdFilter || orderIdFilter;

                const orderQuery = {};
                if (finalOrderDateFilter) {
                    orderQuery.orderDate = finalOrderDateFilter;
                }
                if (finalOrderIdFilter) {
                    orderQuery._id = finalOrderIdFilter;
                }

                let filteredOrderIds = null;
                if (Object.keys(orderQuery).length > 0) {
                    const filteredOrders = await this.$collection('production.orders').find({
                        ...orderQuery,
                        $select: ['_id']
                    });
                    filteredOrderIds = filteredOrders.map(o => o._id);
                    if (filteredOrderIds.length === 0) {
                        return result;
                    }
                }

                const taskQuery = fastCopy(taskFilters);
                if (filteredOrderIds) {
                    taskQuery.orderId = { $in: filteredOrderIds };
                }

                const allTasks = await this.$collection('production.tasks').find({
                    ...taskQuery,
                    $select: ['_id', 'orderId', 'products', 'plannedStartDate', 'order']
                });

                if (!Array.isArray(allTasks) || allTasks.length === 0) {
                    return result;
                }

                const orderIds = [...new Set(allTasks.map(task => task.orderId))];

                const orders = await this.$collection('production.orders').find({
                    _id: {$in: orderIds},
                    $select: ['_id', 'code', 'orderDate', 'productId', 'product', 'branch', 'warehouse', 'status'],
                    $populate: [
                        {
                            path: 'product',
                            select: ['code', 'definition', 'image']
                        },
                        {
                            path: 'branch',
                            select: ['name']
                        },
                        {
                            path: 'warehouse',
                            select: ['name']
                        }
                    ]
                });

                const ordersMap = _.keyBy(orders, '_id');

                const allProcessedData = [];
                const productIds = [];

                for (const task of allTasks) {
                    const products = task.products || [];
                    for (const taskProduct of products) {
                        if (taskProduct.itemType === 'product' && taskProduct.productId && !productIds.includes(taskProduct.productId)) {
                            productIds.push(taskProduct.productId);
                        }
                    }
                }

                let productsMap = {};
                if (productIds.length > 0) {
                    const products = await this.$collection('inventory.products').find({
                        _id: {$in: productIds},
                        $select: ['code', 'definition', 'barcode']
                    });
                    productsMap = _.keyBy(products, '_id');
                }

                for (const task of allTasks) {
                    const products = task.products || [];
                    const orderInfo = ordersMap[task.orderId] || {};

                    for (const taskProduct of products) {
                        if (taskProduct.itemType !== 'product') {
                            continue;
                        }
                        const product = productsMap[taskProduct.productId];
                        const bomItem = {
                            _id: `${task._id}-${taskProduct.productId}-${task.orderId}`,
                            orderId: task.orderId,
                            taskId: task._id,
                            productId: taskProduct.productId,
                            product: product,
                            productCode: taskProduct.code || (product ? product.code : ''),
                            productDefinition: taskProduct.title || (product ? product.definition : ''),
                            plannedQuantity: taskProduct.plannedQuantity || 0,
                            actualQuantity: taskProduct.actualQuantity || 0,
                            plannedCost: taskProduct.plannedCost || 0,
                            actualCost: taskProduct.actualCost || 0,
                            unitId: taskProduct.unitId,
                            unitName: this.$t(taskProduct.unitName),
                            requiredDate: task.plannedStartDate,
                            backflush: !!taskProduct.backflush,
                            order: orderInfo
                        };
                        allProcessedData.push(bomItem);
                    }
                }

                const paginatedData = allProcessedData.slice(startRow, endRow);

                result.data = paginatedData;
                result.total = allProcessedData.length;
                return result;
            } catch (error) {
                console.error('Error in getRows:', error);
                return {
                    total: 0,
                    data: []
                };
            }
        },

        async summaryRow(rows) {
            try {
                const totals = {
                    plannedQuantity: 0,
                    actualQuantity: 0,
                    plannedCost: 0,
                    actualCost: 0,
                    count: 0
                };

                let dataToSum = rows;
                if (!dataToSum || dataToSum.length === 0) {
                    const allTasksResult = await this.getRows({}, {
                        request: {
                            startRow: 0,
                            endRow: Number.MAX_SAFE_INTEGER
                        }
                    });
                    dataToSum = allTasksResult.data;
                }

                dataToSum.forEach(row => {
                    totals.plannedQuantity += _.isNumber(row.plannedQuantity) ? row.plannedQuantity : 0;
                    totals.actualQuantity += _.isNumber(row.actualQuantity) ? row.actualQuantity : 0;
                    totals.plannedCost += _.isNumber(row.plannedCost) ? row.plannedCost : 0;
                    totals.actualCost += _.isNumber(row.actualCost) ? row.actualCost : 0;
                    totals.count += 1;
                });

                return {
                    'order.code': this.$t('TOTAL'),
                    plannedQuantity: totals.plannedQuantity,
                    actualQuantity: totals.actualQuantity,
                    plannedCost: totals.plannedCost,
                    actualCost: totals.actualCost,
                    productCode: `${totals.count} ${this.$t('items')}`
                };
            } catch (error) {
                console.error('Error in summaryRow:', error);
                return {
                    'order.code': this.$t('TOTAL'),
                    productCode: '0 items'
                };
            }
        }
    },

    async created() {
        this.isInitialized = true;
    }
};
</script>

<style lang="scss">
@import 'core';

.production-reports-raw-material-analysis {
    .ui-content {
        display: flex;
        flex-flow: column nowrap;
    }

    .order-items {
        flex: 0 0 100%;
    }
}

.production-order-cell {
    display: flex;
    padding: 10px;

    .order-cell-container {
        display: flex;
        flex-flow: column nowrap;
        justify-content: center;
        align-items: center;
        width: 40px;
        height: 40px;
    }

    .order-cell-image {
        display: flex;
        flex-flow: column nowrap;
        justify-content: center;
        align-items: center;
        border-radius: 6px;
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
    }

    .order-cell-content {
        display: flex;
        flex-flow: column;
        justify-content: center;
        flex: 1;
        margin-left: 10px;
        min-width: 0;
    }

    .order-cell-code {
        width: 100%;
        overflow: hidden;
        font-weight: 700;
        @include text-truncate();
        font-size: 14px;
        line-height: 1;
        margin-bottom: 5px;
    }

    .order-cell-product {
        width: 100%;
        overflow: hidden;
        @include text-truncate();
        font-size: 12px;
        color: #7f878a;
        line-height: 1;
    }
}
</style>