import microtime from 'microtime';
import _ from 'lodash';
import axios from 'axios';
import {toUpper} from 'framework/helpers';

export default [
    {
        name: 'shipping-orders-save',
        async action({data, id}, params) {
            const app = this.app;
            const isCreate = !id;
            const collection = app.collection('logistics.shipping-orders');

            if (!!data.partnerId) {
                const partner = await app.collection('kernel.partners').findOne({
                    _id: data.partnerId,
                    $select: ['code', 'name', 'isCompany', 'email', 'phone', 'identity', 'tin', 'taxDepartment'],
                    $disableSoftDelete: true,
                    $disableActiveCheck: true
                });

                if (_.isPlainObject(partner)) {
                    data.partnerCode = partner.code;
                    data.partnerName = partner.name;
                    data.partnerTinIdentity = partner.isCompany ? partner.tin : partner.identity;
                    data.partnerTaxDepartment = partner.taxDepartment;
                    data.partnerEmail = partner.email;
                    data.partnerPhone = partner.phone;
                }
            }

            if (!!data.carrierId) {
                const carrier = await app.collection('logistics.carriers').findOne({
                    _id: data.carrierId,
                    $select: ['code', 'name', 'integrationType', 'integrationParams'],
                    $disableSoftDelete: true,
                    $disableActiveCheck: true
                });

                if (_.isPlainObject(carrier)) {
                    data.carrierCode = carrier.code;
                    data.carrierName = carrier.name;
                    data.integrationType = carrier.integrationType;
                    data.integrationParams = carrier.integrationParams;
                    data.volumetricWeightFactor = carrier.volumetricWeightFactor;
                }
            }

            if (
                !_.isPlainObject(data.deliveryAddress) ||
                !data.deliveryAddress.countryId ||
                !data.deliveryAddress.city ||
                !data.deliveryAddress.district ||
                !data.deliveryAddress.address
            ) {
                throw new app.errors.Unprocessable(this.translate('Delivery address is invalid!'));
            }

            if (
                !_.isPlainObject(data.warehouseAddress) ||
                !data.warehouseAddress.countryId ||
                !data.warehouseAddress.city ||
                !data.warehouseAddress.district ||
                !data.warehouseAddress.address
            ) {
                throw new app.errors.Unprocessable(this.translate('Warehouse address is invalid!'));
            }

            if (!isCreate) {
                return await collection.patch({_id: id}, data, {user: params.user});
            } else {
                return await collection.create(data, {user: params.user});
            }
        }
    },
    {
        name: 'shipping-orders-approve',
        async action(id, params) {
            const app = this.app;
            const collection = app.collection('logistics.shipping-orders');
            const company = await app.collection('kernel.company').findOne({});
            const order = await collection.get(id);
            const partner = await app.collection('kernel.partners').get(order.partnerId);
            const pkg = await app.collection('logistics.packages').findOne({
                _id: {$in: order.items.map(item => item.packageId)},
                $select: ['items']
            });
            const data = {};
            let payload = {};

            if (order.type === 'normal') {
                if (!!order.integrationType && !!order.integrationParams) {
                    data.carrierType = order.integrationType;
                    data.authParams = JSON.parse(order.integrationParams);
                    data.code = order.code;
                    data.packagingType = order.packagingType;
                    data.isCashOnDelivery = order.cashOnDeliveryAmount > 0;
                    if (data.isCashOnDelivery) data.cashOnDeliveryAmount = order.cashOnDeliveryAmount;
                    data.shippingPaymentType = order.shippingPaymentType;
                    data.currencyCode = (
                        (await app.collection('kernel.currencies').findOne({
                            _id: order.currencyId,
                            $select: ['name'],
                            $disableActiveCheck: true,
                            $disableSoftDelete: true
                        })) || {}
                    ).name;
                    data.sender = {
                        name: company.name,
                        email: company.email,
                        phone: company.phone,
                        taxOffice: company.taxDepartment,
                        taxNumber: company.tin,
                        city: order.warehouseAddress.city,
                        district: order.warehouseAddress.district,
                        address: order.warehouseAddress.address
                    };
                    data.receiver = {
                        name: partner.name,
                        email: partner.email,
                        phone: partner.phone,
                        taxOffice: partner.taxDepartment,
                        taxNumber: partner.tin,
                        city: order.deliveryAddress.city,
                        district: order.deliveryAddress.district,
                        address: order.deliveryAddress.address,
                        subDistrict: order.deliveryAddress.subDistrict,
                        state: order.deliveryAddress.state,
                        postalCode: order.deliveryAddress.postalCode,
                        countryCode: (
                            (await app.collection('kernel.countries').findOne({
                                _id: order.deliveryAddress.countryId,
                                $select: ['code'],
                                $disableActiveCheck: true,
                                $disableSoftDelete: true
                            })) || {}
                        ).code
                    };
                    data.items = order.items.map(item => ({
                        description: item.description ?? '',
                        barcode: item.barcode ?? '',
                        weight: app.round(item.weight, 4) || 1,
                        volumetricWeight: app.round(item.volumetricWeight, 4) || 1
                    }));
                    data.packageItems = pkg?.items?.map(item => ({
                        quantity: item.quantity,
                        unitPrice: item.unitPrice,
                        productCode: item.productCode ?? '',
                        barcode: item.barcode ?? '',
                        productDefinition: item.productDefinition ?? ''
                    }));

                    try {
                        payload = (
                            await axios({
                                method: 'post',
                                url: 'https://api.entererp.com/v1/shipping/create-order',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                data: JSON.stringify(data)
                            })
                        ).data;
                    } catch (error) {
                        console.error(error.response.data);

                        throw new app.errors.Unprocessable(error.response.data);
                    }
                }

                await app.collection('logistics.packages').patch(
                    {_id: {$in: order.items.map(item => item.packageId)}},
                    {
                        isUsed: true
                    },
                    {user: params.user}
                );

                if (!!order.transferId) {
                    const transfer = await app.collection('inventory.transfers').findOne({
                        _id: order.transferId,
                        $select: ['relatedDocuments']
                    });
                    const transferPayload = {
                        carrierId: order.carrierId,
                        cargoTrackingCode: order.code,
                        relatedDocuments: (transfer.relatedDocuments || []).filter(
                            rd => rd.collection !== 'logistics.logistics.shipping-orders'
                        )
                    };
                    transferPayload.relatedDocuments.push({
                        collection: 'logistics.shipping-orders',
                        view: 'logistics.operations.shipping-orders',
                        title: 'Shipping Orders',
                        ids: [order._id]
                    });
                    await app.collection('inventory.transfers').bulkWrite([
                        {
                            updateOne: {
                                filter: {_id: order.transferId},
                                update: {
                                    $set: transferPayload
                                }
                            }
                        }
                    ]);
                }

                if (!!order.orderId) {
                    const saleOrder = await app.collection('sale.orders').findOne({
                        _id: order.orderId,
                        $select: ['relatedDocuments']
                    });
                    const saleOrderPayload = {
                        carrierId: order.carrierId,
                        cargoTrackingCode: order.code,
                        relatedDocuments: (saleOrder.relatedDocuments || []).filter(
                            rd => rd.collection !== 'logistics.logistics.shipping-orders'
                        )
                    };
                    saleOrderPayload.relatedDocuments.push({
                        collection: 'logistics.shipping-orders',
                        view: 'logistics.operations.shipping-orders',
                        title: 'Shipping Orders',
                        ids: [order._id]
                    });
                    await app.collection('sale.orders').bulkWrite([
                        {
                            updateOne: {
                                filter: {_id: order.orderId},
                                update: {
                                    $set: saleOrderPayload
                                }
                            }
                        }
                    ]);
                }

                setTimeout(async () => {
                    try {
                        await app.rpc('logistics.sync-document-shipment-status', {
                            transferId: order.transferId,
                            status: 'order-created',
                            transactionDate: app.datetime.local().toJSDate()
                        });
                    } catch (error) {
                        console.log('Shipment status sync problem ->', error.message);
                    }
                }, 1000);

                return await collection.patch(
                    {_id: id},
                    {
                        status: 'order-created',
                        records: [
                            {
                                date: app.datetime.local().toJSDate(),
                                status: 'order-created',
                                description: this.translate('Order created'),
                                ...(!!order.integrationType && !!order.integrationParams
                                    ? {
                                          location: data.sender.city,
                                          locationPhone: data.sender.phone,
                                          locationAddress: `${data.sender.district} / ${data.sender.city}`
                                      }
                                    : {})
                            }
                        ],
                        shipmentPayload: !!order.integrationType && !!order.integrationParams ? payload : {}
                    },
                    {user: params.user}
                );
            } else if (order.type === 'return') {
                const originalOrder = await app.collection('sale.orders').findOne({
                    _id: order.orderId,
                    $select: ['deliveryAddress'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
                const customerAddress =
                    originalOrder && originalOrder.deliveryAddress ? originalOrder.deliveryAddress : partner.address;

                if (!!order.integrationType && !!order.integrationParams) {
                    data.carrierType = order.integrationType;
                    data.authParams = JSON.parse(order.integrationParams);
                    data.code = order.code;
                    data.packagingType = order.packagingType;
                    data.isCashOnDelivery = order.cashOnDeliveryAmount > 0;
                    if (data.isCashOnDelivery) data.cashOnDeliveryAmount = order.cashOnDeliveryAmount;
                    data.shippingPaymentType = order.shippingPaymentType;
                    data.currencyCode = (
                        (await app.collection('kernel.currencies').findOne({
                            _id: order.currencyId,
                            $select: ['name'],
                            $disableActiveCheck: true,
                            $disableSoftDelete: true
                        })) || {}
                    ).name;
                    data.sender = {
                        name: company.name,
                        email: company.email,
                        phone: company.phone,
                        taxOffice: company.taxDepartment,
                        taxNumber: company.tin,
                        city: order.warehouseAddress.city,
                        district: order.warehouseAddress.district,
                        address: order.warehouseAddress.address
                    };
                    data.receiver = {
                        name: partner.name,
                        email: partner.email,
                        phone: partner.phone,
                        taxOffice: partner.taxDepartment,
                        taxNumber: partner.tin,
                        city: customerAddress.city,
                        district: customerAddress.district,
                        address: customerAddress.address,
                        subDistrict: customerAddress.subDistrict,
                        state: customerAddress.state,
                        postalCode: customerAddress.postalCode,
                        countryCode: (
                            (await app.collection('kernel.countries').findOne({
                                _id: customerAddress.countryId,
                                $select: ['code'],
                                $disableActiveCheck: true,
                                $disableSoftDelete: true
                            })) || {}
                        ).code
                    };
                    data.items = order.items.map(item => ({
                        description: item.description ?? '',
                        barcode: item.barcode ?? '',
                        weight: app.round(item.weight, 4) || 1,
                        volumetricWeight: app.round(item.volumetricWeight, 4) || 1
                    }));
                    data.packageItems = pkg?.items?.map(item => ({
                        quantity: item.quantity,
                        unitPrice: item.unitPrice,
                        productCode: item.productCode ?? '',
                        barcode: item.barcode ?? '',
                        productDefinition: item.productDefinition ?? ''
                    }));

                    let payload = {};
                    try {
                        payload = (
                            await axios({
                                method: 'post',
                                url: 'https://api.entererp.com/v1/shipping/create-return-order',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                data: JSON.stringify(data)
                            })
                        ).data;
                    } catch (error) {
                        console.error(error.response.data);

                        throw new app.errors.Unprocessable(error.response.data);
                    }
                }

                await app.collection('logistics.packages').patch(
                    {_id: {$in: order.items.map(item => item.packageId)}},
                    {
                        isUsed: true
                    },
                    {user: params.user}
                );

                return await collection.patch(
                    {_id: id},
                    {
                        status: 'order-created',
                        records: [
                            {
                                date: app.datetime.local().toJSDate(),
                                status: 'order-created',
                                description: this.translate('Order created'),
                                ...(!!order.integrationType && !!order.integrationParams
                                    ? {
                                          location: data.sender.city,
                                          locationPhone: data.sender.phone,
                                          locationAddress: `${data.sender.district} / ${data.sender.city}`
                                      }
                                    : {})
                            }
                        ],
                        shipmentPayload: !!order.integrationType && !!order.integrationParams ? payload : {}
                    },
                    {user: params.user}
                );
            }
        }
    },
    {
        name: 'shipping-orders-cancel',
        async action(id, params) {
            const app = this.app;
            const collection = app.collection('logistics.shipping-orders');
            const order = await collection.get(id);

            if (order.status !== 'draft' && !!order.integrationType && !!order.integrationParams) {
                const data = {};
                data.carrierType = order.integrationType;
                data.authParams = JSON.parse(order.integrationParams);
                data.code = order.code;
                data.shipmentPayload = order.shipmentPayload;

                await axios({
                    method: 'post',
                    url: 'https://api.entererp.com/v1/shipping/cancel-order',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: JSON.stringify(data)
                });
            }

            if (!!order.transferId) {
                const transfer = await app.collection('inventory.transfers').findOne({
                    _id: order.transferId,
                    $select: ['relatedDocuments']
                });
                const transferPayload = {
                    carrierId: '',
                    cargoTrackingCode: '',
                    relatedDocuments: (transfer.relatedDocuments || [])
                        .filter(rd => rd.collection !== 'logistics.packages')
                        .filter(rd => rd.collection !== 'logistics.shipping-orders')
                };
                await app.collection('inventory.transfers').bulkWrite([
                    {
                        updateOne: {
                            filter: {_id: order.transferId},
                            update: {
                                $set: transferPayload
                            }
                        }
                    }
                ]);
            }
            if (!!order.orderId) {
                const saleOrder = await app.collection('sale.orders').findOne({
                    _id: order.orderId,
                    $select: ['relatedDocuments']
                });
                const orderPayload = {
                    carrierId: '',
                    cargoTrackingCode: '',
                    relatedDocuments: (saleOrder.relatedDocuments || []).filter(
                        rd => rd.collection !== 'logistics.shipping-orders'
                    )
                };
                await app.collection('sale.orders').bulkWrite([
                    {
                        updateOne: {
                            filter: {_id: order.orderId},
                            update: {
                                $set: orderPayload
                            }
                        }
                    }
                ]);
            }

            return await collection.patch(
                {_id: id},
                {
                    status: 'canceled'
                },
                {user: params.user}
            );
        }
    },

    {
        name: 'shipping-orders-calculate',
        async action(id, params) {
            const app = this.app;
            const collection = app.collection('logistics.shipping-orders');
            const order = await collection.get(id);
            const partner = await app.collection('kernel.partners').get(order.partnerId);
            const data = {};

            data.carrierType = order.integrationType;
            data.authParams = JSON.parse(order.integrationParams);
            data.packagingType = order.packagingType;
            data.shippingPaymentType = order.shippingPaymentType;
            data.city = partner.address.city;
            data.district = partner.address.district;
            data.address = partner.address.address;
            data.items = order.items.map(item => ({
                description: item.description,
                barcode: item.barcode,
                weight: app.round(item.weight, 4) || 1,
                volumetricWeight: app.round(item.volumetricWeight, 4) || 1
            }));

            let result = {};
            try {
                result = (
                    await axios({
                        method: 'post',
                        url: 'https://api.entererp.com/v1/shipping/calculate',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        data: JSON.stringify(data)
                    })
                ).data;
            } catch (error) {
                console.error(error.response.data);

                throw new app.errors.Unprocessable(error.response.data);
            }

            await collection.patch(
                {_id: id},
                {
                    estimatedCos: result
                },
                {user: params.user}
            );

            return result;
        }
    },
    {
        name: 'shipping-orders-get-code',
        async action({carrierId}) {
            const app = this.app;

            let shippingOrderCode = microtime.now();

            if (!carrierId) return shippingOrderCode;

            const carrier = await app.collection('logistics.carriers').findOne({
                _id: carrierId,
                $select: ['integrationParams', 'integrationType'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });

            if (carrier?.integrationType === 'hepsijet') {
                try {
                    const integrationParams = JSON.parse(carrier.integrationParams);

                    if (!integrationParams.abbreviationCode) {
                        throw new app.errors.Unprocessable('Carrier abbreviation code is required!');
                    }

                    const shortenedAbbreviationCode = toUpper(integrationParams.abbreviationCode).slice(0, 3);
                    const shortenedIdentifier = `${microtime.now()}`.slice(3);

                    shippingOrderCode = `${shortenedAbbreviationCode}${shortenedIdentifier}`;
                } catch (error) {
                    throw new app.errors.Unprocessable(error.message);
                }
            }

            return shippingOrderCode;
        }
    },
    {
        name: 'shipping-orders-get-merged-label',
        async action({id}) {
            const app = this.app;
            const {PDFDocument} = await import('pdf-lib');

            let templateId = null;
            if (typeof arguments[0] === 'object' && arguments[0] !== null && arguments[0].templateId) {
                templateId = arguments[0].templateId;
            }

            const order = await app.collection('logistics.shipping-orders').get(id);
            const carrier = await app.collection('logistics.carriers').findOne({
                _id: order.carrierId,
                $select: ['printingMode', 'integrationType', 'integrationParams', 'defaultPrintingTemplateId']
            });

            if (!carrier || carrier.printingMode !== 'hepsijet-system-template') {
                throw new app.errors.Unprocessable('This method is only for HepsiJET + System Template mode');
            }

            try {
                // Get HepsiJET barcode
                const data = {};
                data.carrierType = carrier.integrationType;
                data.authParams = JSON.parse(carrier.integrationParams);
                data.code = order.code;
                data.shipmentPayload = order.shipmentPayload;

                const hepsijetResponse = (
                    await axios({
                        method: 'post',
                        url: 'https://api.entererp.com/v1/shipping/get-barcode',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        data: JSON.stringify(data)
                    })
                ).data;

                if (!hepsijetResponse || !hepsijetResponse.barcode) {
                    throw new app.errors.Unprocessable('Could not get HepsiJET barcode');
                }

                let hepsijetPdfBuffer;
                if (hepsijetResponse.barcode.startsWith('data:application/pdf;base64,')) {
                    hepsijetPdfBuffer = Buffer.from(hepsijetResponse.barcode.split(',')[1], 'base64');
                } else if (hepsijetResponse.barcode.startsWith('%PDF')) {
                    hepsijetPdfBuffer = Buffer.from(hepsijetResponse.barcode, 'binary');
                } else {
                    try {
                        hepsijetPdfBuffer = Buffer.from(hepsijetResponse.barcode, 'base64');
                        const pdfHeader = hepsijetPdfBuffer.toString('ascii', 0, 4);
                        if (pdfHeader !== '%PDF') {
                            throw new Error('Not a valid PDF');
                        }
                    } catch (error) {
                        console.error('HepsiJET barcode is not a valid PDF:', error.message);
                        throw new app.errors.Unprocessable('HepsiJET barcode is not in PDF format');
                    }
                }

                // Get all products from all packages
                let allProducts = [];
                const soItems = Array.isArray(order.items) ? order.items : [];

                for (const soItem of soItems) {
                    if (soItem.packageId) {
                        const pkg = await app.collection('logistics.packages').findOne({
                            _id: soItem.packageId,
                            $select: ['items'],
                            $disableActiveCheck: true,
                            $disableSoftDelete: true
                        });

                        if (pkg && Array.isArray(pkg.items) && pkg.items.length > 0) {
                            for (const packageItem of pkg.items) {
                                allProducts.push({
                                    productCode: packageItem.productCode,
                                    productDefinition: packageItem.productDefinition,
                                    quantity: packageItem.quantity,
                                    unit: packageItem.unitName
                                });
                            }
                        }
                    }
                }

                // Paginate products
                const PRODUCTS_PER_PAGE = 5;
                const totalProducts = allProducts.length;
                const totalPages = Math.ceil(totalProducts / PRODUCTS_PER_PAGE);

                const mergedPdf = await PDFDocument.create();
                const hepsijetPdf = await PDFDocument.load(hepsijetPdfBuffer);
                const hepsijetEmbeddedPage = await mergedPdf.embedPage(hepsijetPdf.getPages()[0]);
                const hepsijetPageSize = hepsijetEmbeddedPage.size();

                // Generate pages for each product batch
                for (let pageIndex = 0; pageIndex < Math.max(1, totalPages); pageIndex++) {
                    const startIndex = pageIndex * PRODUCTS_PER_PAGE;
                    const endIndex = Math.min(startIndex + PRODUCTS_PER_PAGE, totalProducts);
                    const pageProducts = allProducts.slice(startIndex, endIndex);

                    let systemTemplatePdfBuffer;
                    try {
                        let templateKey = null;
                        if (templateId) {
                            templateKey = templateId;
                        } else if (carrier.defaultPrintingTemplateId) {
                            templateKey = carrier.defaultPrintingTemplateId;
                        } else {
                            templateKey = 'logistics.shipping-order-label';
                        }

                        let template = null;
                        let templateData = null;
                        let templateContent = null;

                        if (templateKey && typeof templateKey === 'string' && templateKey.length === 24) {
                            const templateDoc = await app.collection('kernel.templates').findOne({ _id: templateKey });
                            if (templateDoc) {
                                const defaultTemplate = app.get('templates')['logistics.shipping-order-label'];
                                template = {
                                    outputFormat: templateDoc.outputFormat,
                                    outputOrientation: templateDoc.outputOrientation,
                                    outputWidth: templateDoc.outputWidth,
                                    outputHeight: templateDoc.outputHeight,
                                    record: async (app, id, params) => {
                                        if (templateDoc.recordScript) {
                                            // eslint-disable-next-line no-eval
                                            return await eval(templateDoc.recordScript)(app, id, params);
                                        } else if (defaultTemplate && typeof defaultTemplate.record === 'function') {
                                            return await defaultTemplate.record(app, id, params);
                                        } else {
                                            return await app.collection('logistics.shipping-orders').get(id);
                                        }
                                    },
                                    content: async () => templateDoc.content
                                };
                                templateData = await template.record(app, id, {pageProducts, pageIndex, totalPages});
                                templateContent = await template.content(app, id);
                            }
                        }

                        if (!template) {
                            template = app.get('templates')[templateKey];
                            if (!template) {
                                throw new Error('Template not found. templateKey: ' + templateKey);
                            }
                            templateData = await template.record(app, id, {pageProducts, pageIndex, totalPages});
                            templateContent = await template.content(app, id);
                        }

                        if (templateData && templateData.items && templateData.items.length > 0) {
                            templateData.items[0].subItems = pageProducts;
                            templateData.items[0].pageInfo = {
                                currentPage: pageIndex + 1,
                                totalPages: Math.max(1, totalPages),
                                totalProducts: totalProducts
                            };
                        }

                        const compile = await import('framework/helpers/compile-hbs');
                        const compiledContent = await compile.default(app, templateContent, templateData);

                        const generatePDF = await import('framework/helpers/generate-pdf');
                        const pdfOptions = {
                            ...(template.outputFormat !== 'custom' ? {format: template.outputFormat} : {}),
                            ...(template.outputFormat === 'custom'
                                ? {
                                      width: _.isNumber(template.outputWidth) ? `${template.outputWidth}mm` : template.outputWidth,
                                      height: _.isNumber(template.outputHeight) ? `${template.outputHeight}mm` : template.outputHeight
                                  }
                                : {}),
                            landscape: template.outputOrientation === 'landscape',
                            printBackground: true
                        };

                        systemTemplatePdfBuffer = await generatePDF.default(compiledContent, pdfOptions);
                    } catch (templateError) {
                        console.error('Template error:', templateError);
                        throw new app.errors.Unprocessable(`Could not generate system template PDF: ${templateError.message}`);
                    }

                    const systemTemplatePdf = await PDFDocument.load(systemTemplatePdfBuffer);
                    const systemEmbeddedPage = await mergedPdf.embedPage(systemTemplatePdf.getPages()[0]);
                    const systemPageSize = systemEmbeddedPage.size();

                    const combinedPage = mergedPdf.addPage([
                        systemPageSize.width,
                        systemPageSize.height
                    ]);

                    combinedPage.drawPage(systemEmbeddedPage, {
                        x: 0,
                        y: 0,
                        width: systemPageSize.width,
                        height: systemPageSize.height
                    });

                    combinedPage.drawPage(hepsijetEmbeddedPage, {
                        x: 0,
                        y: 0,
                        width: hepsijetPageSize.width + 50,
                        height: hepsijetPageSize.height * 0.9
                    });
                }

                // Save merged PDF
                const mergedPdfBytes = await mergedPdf.save();
                const base64Pdf = Buffer.from(mergedPdfBytes).toString('base64');

                return `data:application/pdf;base64,${base64Pdf}`;

            } catch (error) {
                throw new app.errors.Unprocessable(error.message);
            }
        }
    },
    {
        name: 'shipping-orders-get-barcode',
        async action({id}) {
            const app = this.app;
            const order = await app.collection('logistics.shipping-orders').get(id);

            if (!!order.integrationType && !!order.integrationParams) {
                const data = {};
                data.carrierType = order.integrationType;
                data.authParams = JSON.parse(order.integrationParams);
                data.code = order.code;
                data.shipmentPayload = order.shipmentPayload;

                try {
                    const response = (
                        await axios({
                            method: 'post',
                            url: 'https://api.entererp.com/v1/shipping/get-barcode',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            data: JSON.stringify(data)
                        })
                    ).data;

                    if (_.isPlainObject(response) && response.barcode) {
                        return response.barcode;
                    }
                } catch (error) {
                    throw new app.errors.Unprocessable(error.response.data);
                }
            }
        }
    },
    {
        name: 'shipping-orders-get-tracking-url',
        async action({carrierId, cargoTrackingCode}) {
            const app = this.app;
            let url = null;

            if (!carrierId) return null;

            const carrier = await app.collection('logistics.carriers').findOne({
                _id: carrierId,
                $select: ['integrationParams', 'integrationType'],
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });

            try {
                if (carrier) {
                    const integrationParams = JSON.parse(carrier.integrationParams);

                    if (
                        typeof integrationParams.cargoTrackingUrlTemplate === 'string' &&
                        integrationParams.cargoTrackingUrlTemplate.trim().length > 0 &&
                        integrationParams.cargoTrackingUrlTemplate.startsWith('http')
                    ) {
                        url = integrationParams.cargoTrackingUrlTemplate
                            .trim()
                            .replace('{{cargoTrackingCode}}', cargoTrackingCode);
                    }
                }
            } catch (error) {}

            return url;
        }
    }
];
